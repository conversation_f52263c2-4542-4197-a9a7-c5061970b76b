#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货和指数价格监控系统
主要监控期货基差的成本
"""

import pandas as pd
import numpy as np
import akshare as ak
import adata
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.width', 180)
warnings.filterwarnings('ignore') 

class FuturesMonitor:
    """期货监控系统主类"""
    
    def __init__(self):
        """初始化监控系统"""
        # 期货品种与指数的映射关系
        self.futures_index_mapping = {
            'IC': {'name': '中证500', 'code': '000905'},
            'IM': {'name': '中证1000', 'code': '000852'},
            'IF': {'name': '沪深300', 'code': '000300'},
            'IH': {'name': '上证50', 'code': '000016'}
        }

        # 期货合约代码与行情symbol的映射关系
        self.contract_symbol_mapping = {
            'IC': '中证500指数期货',
            'IM': '中证1000指数期货',
            'IF': '沪深300指数期货',
            'IH': '上证50指数期货'
        }
        
        # 年化交易日数
        self.annual_trading_days = 243
        
        # 缓存数据
        self.trading_calendar = None
        self.futures_contracts = None
        
    def get_trading_calendar(self, year=None):
        """获取交易日历"""
        if year is None:
            year = datetime.now().year
            
        try:
            calendar_df = adata.stock.info.trade_calendar(year=year)
            # 只保留交易日
            trading_days = calendar_df[calendar_df['trade_status'] == 1]['trade_date'].tolist()
            self.trading_calendar = trading_days
            return trading_days
        except Exception as e:
            print(f"获取交易日历失败: {e}")
            return []
    
    def calculate_remaining_trading_days(self, expiry_date):
        """计算合约剩余交易日"""
        if self.trading_calendar is None:
            self.get_trading_calendar()
            
        today = datetime.now().date()
        if isinstance(expiry_date, str):
            expiry_date = datetime.strptime(expiry_date, '%Y-%m-%d').date()
        
        # 计算今天到到期日之间的交易日数量
        remaining_days = 0
        for trade_date in self.trading_calendar:
            if isinstance(trade_date, str):
                trade_date = datetime.strptime(trade_date, '%Y-%m-%d').date()
            if today <= trade_date <= expiry_date:
                remaining_days += 1
                
        return remaining_days
    
    def calculate_trading_days(self, start_date, end_date):
        """计算合约剩余交易日"""
        if self.trading_calendar is None:
            self.get_trading_calendar()
            
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        # 计算今天到到期日之间的交易日数量
        remaining_days = 0
        for trade_date in self.trading_calendar:
            if isinstance(trade_date, str):
                trade_date = datetime.strptime(trade_date, '%Y-%m-%d').date()
            if start_date <= trade_date <= end_date:
                remaining_days += 1
        return remaining_days
    
    def get_futures_contracts_info(self, date=None):
        """获取期货合约信息"""
        if date is None:
            date = datetime.now().strftime('%Y%m%d')
        
        try:
            contracts_df = ak.futures_contract_info_cffex(date=date)
            
            # 过滤出股指期货合约
            stock_index_futures = contracts_df[
                contracts_df['品种'].isin(['IC', 'IM', 'IF', 'IH'])
            ].copy()
            
            # 解析合约代码获取品种和月份
            stock_index_futures['品种代码'] = stock_index_futures['合约代码'].str[:2]
            stock_index_futures['合约月份_parsed'] = stock_index_futures['合约代码'].str[2:]
            
            # 过滤掉连续合约，只保留真实月份合约
            real_contracts = stock_index_futures[
                stock_index_futures['合约月份_parsed'].str.len() == 4
            ].copy()
            
            self.futures_contracts = real_contracts
            return real_contracts
            
        except Exception as e:
            print(f"获取期货合约信息失败: {e}")
            return pd.DataFrame()
    
    def get_futures_quotes(self, contracts):
        """获取期货行情数据"""
        if contracts.empty:
            return pd.DataFrame()
            
        try:
            # 构建合约代码列表
            contract_symbols = contracts['合约代码'].tolist()
            symbol_str = ','.join(contract_symbols)
            
            # 获取行情数据
            quotes_df = ak.futures_zh_spot(symbol=symbol_str, market="FF", adjust='0')
            
            return quotes_df
            
        except Exception as e:
            print(f"获取期货行情数据失败: {e}")
            return pd.DataFrame()
    
    def get_index_quotes(self, index_codes):
        """获取指数行情数据"""
        index_data = {}
        
        for code in index_codes:
            try:
                quote_df = adata.stock.market.get_market_index_current(index_code=code)
                if not quote_df.empty:
                    index_data[code] = quote_df.iloc[0]['price']
            except Exception as e:
                print(f"获取指数 {code} 行情失败: {e}")
                index_data[code] = np.nan
                
        return index_data
    
    def calculate_basis_metrics(self, futures_price, index_price, remaining_days, div_adjust, volume, total_volume_by_product):
        """计算基差相关指标"""
        if pd.isna(futures_price) or pd.isna(index_price) or remaining_days <= 0:
            return {
                'basis': np.nan,
                'basis_ratio': np.nan,
                'annual_hedge_cost': np.nan,
                'annual_hedge_cost_no_dividend': np.nan,
                'volume_ratio': np.nan
            }
        
        # 基差 = 期货价格 - 指数价格
        basis = futures_price - index_price
        
        # 基差幅度 = 基差 / 指数价格 * 100%
        basis_ratio = (basis / index_price) * 100
        
        # 年化对冲成本 = 基差 / 指数价格 / 剩余交易日 * 年化交易日数 * 100%
        annual_hedge_cost = ((basis + div_adjust) / index_price / remaining_days * self.annual_trading_days) * 100
        
        # 年化对冲(no分红) = 年化对冲成本 (暂时相同，分红预估后续添加)
        annual_hedge_cost_no_dividend = (basis / index_price / remaining_days * self.annual_trading_days) * 100
        
        # 成交占比
        volume_ratio = (volume / total_volume_by_product * 100) if total_volume_by_product > 0 else 0
        
        return {
            'basis': basis,
            'basis_ratio': basis_ratio,
            'annual_hedge_cost': annual_hedge_cost,
            'annual_hedge_cost_no_dividend': annual_hedge_cost_no_dividend,
            'volume_ratio': volume_ratio
        }
    
    def calculate_target_costs(self, contracts_data, current_contract):
        """计算合约间转换成本"""
        target_costs = {}
        
        if current_contract not in contracts_data.index:
            return target_costs
            
        current_basis = contracts_data.loc[current_contract, '基差'] + contracts_data.loc[current_contract, '分红预估']
        current_remaining_days = contracts_data.loc[current_contract, '合约剩余交易日']
        index_price = contracts_data.loc[current_contract, '指数最新价']
        
        # 获取同品种的其他合约
        current_product = current_contract[:2]
        same_product_contracts = [c for c in contracts_data.index if c.startswith(current_product) and c != current_contract]
        
        for target_contract in same_product_contracts:
            target_basis = contracts_data.loc[target_contract, '基差'] + contracts_data.loc[target_contract, '分红预估']
            target_remaining_days = contracts_data.loc[target_contract, '合约剩余交易日']
            
            if pd.notna(current_basis) and pd.notna(target_basis) and current_remaining_days > 0 and target_remaining_days > 0:
                # 计算基差差值的年化成本
                basis_diff = ( current_basis - target_basis )
                days_diff = (current_remaining_days - target_remaining_days)
                
                if days_diff != 0:
                    # 年化转换成本 = (基差差值 / 天数差) * 年化交易日数
                    annual_conversion_cost = basis_diff / days_diff /index_price  * self.annual_trading_days
                    target_costs[f'target_{target_contract}'] = annual_conversion_cost
                else:
                    target_costs[f'target_{target_contract}'] = 0
            else:
                target_costs[f'target_{target_contract}'] = np.nan
                
        return target_costs

    def run_monitor(self):
        """运行监控系统，生成完整的监控数据"""
        print("开始运行期货监控系统...")

        # 1. 获取期货合约信息
        print("1. 获取期货合约信息...")
        contracts_info = self.get_futures_contracts_info()
        if contracts_info.empty:
            print("无法获取期货合约信息")
            return pd.DataFrame()

        # 2. 获取期货行情数据
        print("2. 获取期货行情数据...")
        futures_quotes = self.get_futures_quotes(contracts_info)
        if futures_quotes.empty:
            print("无法获取期货行情数据")
            return pd.DataFrame()

        # 3. 获取指数行情数据
        print("3. 获取指数行情数据...")
        index_codes = list(set([mapping['code'] for mapping in self.futures_index_mapping.values()]))
        index_quotes = self.get_index_quotes(index_codes)

        # 4. 整合数据
        print("4. 整合和计算数据...")
        result_data = []

        # 5. 读取分红数据
        print("5. 读取分红数据...")
        div_file = 'futures_div_estimate.csv'
        div = pd.read_csv(div_file).to_dict('records')[-1]
        div_report_date = pd.to_datetime(div['date']).date()
        print(f'分红报告日期: {div_report_date}')
        


        # 按品种分组计算成交量占比
        volume_by_product = {}
        for _, contract in contracts_info.iterrows():
            product = contract['品种']
            contract_code = contract['合约代码']

            # 查找对应的行情数据 - 使用映射关系
            symbol_prefix = self.contract_symbol_mapping.get(product, '')
            contract_month = contract['合约月份']
            expected_symbol = f"{symbol_prefix}{contract_month}"

            quote_row = futures_quotes[futures_quotes['symbol'] == expected_symbol]
            if not quote_row.empty:
                volume = quote_row.iloc[0]['volume']
                if product not in volume_by_product:
                    volume_by_product[product] = 0
                volume_by_product[product] += volume

        # 处理每个合约
        for _, contract in contracts_info.iterrows():
            contract_code = contract['合约代码']
            product = contract['品种']
            expiry_date = contract['最后交易日']

            # 获取期货行情 - 使用映射关系
            symbol_prefix = self.contract_symbol_mapping.get(product, '')
            contract_month = contract['合约月份']
            expected_symbol = f"{symbol_prefix}{contract_month}"

            quote_row = futures_quotes[futures_quotes['symbol'] == expected_symbol]
            if quote_row.empty:
                continue

            futures_price = quote_row.iloc[0]['current_price']
            volume = quote_row.iloc[0]['volume']

            # 获取对应指数行情
            index_info = self.futures_index_mapping.get(product, {})
            index_code = index_info.get('code')
            index_name = index_info.get('name', product)
            index_price = index_quotes.get(index_code, np.nan)

            # 计算剩余交易日
            remaining_days = self.calculate_remaining_trading_days(expiry_date)

            # 计算分红调整
            div_adjust = div.get(f'{contract_code}')
            div_days = self.calculate_trading_days(div_report_date, expiry_date)
            div_adjust = div_adjust * remaining_days / div_days 
            # print(f'contract_code: {contract_code}, div_adjust: {div_adjust}')

            # 计算基差指标
            total_volume = volume_by_product.get(product, 1)
            metrics = self.calculate_basis_metrics(
                futures_price, index_price, remaining_days, div_adjust, volume, total_volume
            )

            # 构建结果行
            row_data = {
                '合约代码': contract_code,
                '到期日': expiry_date,
                '合约剩余交易日': remaining_days,
                '合约最新价': futures_price,
                '指数最新价': index_price,
                '分红预估': div_adjust,
                '基差': metrics['basis'],
                '基差幅度': metrics['basis_ratio'],
                '年化对冲成本': metrics['annual_hedge_cost'],
                '年化对冲(no分红)': metrics['annual_hedge_cost_no_dividend'],
                '成交量': volume,
                '成交占比': metrics['volume_ratio'],
                '品种': product,
                '指数名称': index_name
            }

            result_data.append(row_data)

        # 转换为DataFrame
        result_df = pd.DataFrame(result_data)

        if result_df.empty:
            print("没有有效的数据")
            return pd.DataFrame()

        # 5. 计算合约间转换成本
        print("5. 计算合约间转换成本...")
        result_df.set_index('合约代码', inplace=True)

        # 为每个合约计算target成本
        for contract_code in result_df.index:
            target_costs = self.calculate_target_costs(result_df, contract_code)
            for target_col, cost in target_costs.items():
                result_df.loc[contract_code, target_col] = cost

        result_df.reset_index(inplace=True)

        print("监控数据生成完成!")
        print('')
        return result_df

    def format_output(self, data_df):
        """格式化输出数据，按品种分组"""
        if data_df.empty:
            return ""

        output_lines = []

        data_df.sort_values(by='合约代码', inplace=True)
        data_df.fillna(0, inplace=True)

        # print(data_df.head(20))
        # 按品种分组
        for product in ['IC', 'IM', 'IF', 'IH']:
            l = []
            product_data = data_df[data_df['品种'] == product].copy()
            # print(f'product_data: {product_data}')
            if product_data.empty:
                continue

            # 获取品种对应的指数名称
            index_name = self.futures_index_mapping.get(product, {}).get('name', product)
            # print(product_data)
            for _, row in product_data.iterrows():
                r = {}

                r['合约代码'] = row['合约代码']
                r['到期日'] = row['到期日']
                r['合约剩余交易日'] = row['合约剩余交易日']
                r['合约最新价'] = row['合约最新价']
                r['指数最新价'] = row['指数最新价']
                r['分红预估'] = f'{row["分红预估"]:.2f}'
                r['基差'] = row['基差']
                r['基差幅度'] = f'{row['基差幅度']:.2f}%'
                # r['基差幅度'] = row['基差幅度']
                r['年化对冲成本'] = f'{row['年化对冲成本']:.2f}%'
                r['年化对冲(no分红)'] = f'{row['年化对冲(no分红)']:.2f}%'
                r['成交量'] = row['成交量']
                r['成交占比'] = f'{row['成交占比']:.2f}%'
                
                r['base'] = f'base_{row['合约代码']}'
                target_columns = [col for col in product_data.columns if col.startswith(f'target_{product}')]
                target_columns.sort()
                for target_col in target_columns:
                    r[target_col] = f'{row[target_col]:.2%}'

                l.append(r)
                # print(l)
            res_df = pd.DataFrame(l)
            print(f'{index_name}: \n{res_df}\n')
            
            
            
            
            # product_data = data_df[data_df['品种'] == product].copy()
            # # print(f'product_data: {product_data}')
            # if product_data.empty:
            #     continue

            # # 获取品种对应的指数名称
            # index_name = self.futures_index_mapping.get(product, {}).get('name', product)
            # output_lines.append(f"\n{index_name}")

            # # 准备列名
            # base_columns = [
            #     '合约代码', '到期日', '合约剩余交易日', '合约最新价', '指数最新价',
            #     '分红预估', '基差', '基差幅度', '年化对冲成本', '年化对冲(no分红)',
            #     '成交量', '成交占比'
            # ]

            # # 添加target列
            # target_columns = [col for col in product_data.columns if col.startswith(f'target_{product}')]
            # all_columns = base_columns + target_columns
            # # print(f'all_columns: {all_columns}')

            # # 选择存在的列
            # available_columns = [col for col in all_columns if col in product_data.columns]

            # # 输出表头
            # header = ','.join(available_columns)
            # output_lines.append(header)

            # # 输出数据行
            # for _, row in product_data.iterrows():
            #     row_values = []
            #     for col in available_columns:
            #         value = row[col]
            #         if pd.isna(value):
            #             row_values.append('')
            #         elif isinstance(value, float):
            #             if col in ['基差幅度', '年化对冲成本', '年化对冲(no分红)', '成交占比']:
            #                 row_values.append(f'{value:.2f}')
            #             else:
            #                 row_values.append(f'{value:.4f}')
            #         else:
            #             row_values.append(str(value))

            #     output_lines.append(','.join(row_values))

            # output_lines.append('')  # 空行分隔

        # return '\n'.join(output_lines)

    def save_to_csv(self, data_df, filename='futures_monitor_output.csv'):
        """保存数据到CSV文件"""
        if data_df.empty:
            print("没有数据可保存")
            return

        try:
            # 按品种分组保存
            with open(filename, 'w', encoding='utf-8-sig') as f:
                f.write(self.format_output(data_df))

            print(f"数据已保存到 {filename}")

        except Exception as e:
            print(f"保存文件失败: {e}")

    def read_from_csv(self, filename='futures_monitor_output.csv'):
        """从CSV文件读取数据"""
        try:
            data_df = pd.read_csv(filename, encoding='utf-8-sig')
            return data_df
        except Exception as e:
            print(f"读取文件失败: {e}")
            return pd.DataFrame()


import os
def clear_screen():
    """清空控制台屏幕"""
    os.system('cls' if os.name == 'nt' else 'clear')
    

def main():
    import time
    """主函数"""
    monitor = FuturesMonitor()

    # 在每天09:15 - 15:01 之间, 每隔 15s 运行一次
    while True:
        if datetime.now() > datetime.now().replace(hour=9, minute=25) and \
          datetime.now() < datetime.now().replace(hour=15, minute=1):
            result_df = monitor.run_monitor()
            clear_screen()
            print(f"--- 金融股指期货监控面板 (更新于: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) ---")
            print('')
            if not result_df.empty:
                monitor.format_output(result_df)
        time.sleep(15)



    # # 运行监控
    # result_df = monitor.run_monitor()

    # if not result_df.empty:
    #     # 显示结果
    #     monitor.format_output(result_df)

    #     # 保存到文件
    #     # monitor.save_to_csv(result_df)

    #     # 读取文件
    #     # print("\n=== 监控结果 ===")
    #     # result_df = monitor.read_from_csv() 
    #     # print(result_df)

    #     # 显示统计信息
    #     # print(f"\n=== 统计信息 ===")
    #     # print(f"总合约数: {len(result_df)}")
    #     # for product in result_df['品种'].unique():
    #     #     count = len(result_df[result_df['品种'] == product])
    #     #     print(f"{product}: {count} 个合约")
    # else:
    #     print("未获取到有效数据")


if __name__ == "__main__":
    main()
