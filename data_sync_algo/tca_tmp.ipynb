import json
import datetime
import efinance as ef
from utils import mysql
import pandas as pd
# from data.raw_data import mins_data
import requests
from pqdm.processes import pqdm
import datetime
import adata

def calc_cost(avg_px,bm_px,side):
    return  side*(avg_px/bm_px -1 )*10000
def calc(mktdata,po,f):
    idata=mktdata[(mktdata['time']>po['start_time'])&(mktdata['time']<=po['end_time'])]
    if idata['qty'].sum()<=0:
        vwap=po['filled_price']
    else:
        if f:
            vwap=idata['amt'].sum()/idata['qty'].sum()
        else:
            vwap=idata['amt'].sum()/idata['qty'].sum()/100
    if vwap<=0:
        po.update({'vwap':0,'vwap_cost':0})
        return po
    vwap_cost=calc_cost(po['filled_price'],vwap,po['side'])
    po.update({'vwap':vwap,'vwap_cost':vwap_cost})
    return po

def stats(res):
    d={}
    d['wavg_cost']=-(res['vwap_cost']*res['executed_notional']/res['executed_notional'].sum()).sum()
    d['avg_cmp_rate']=res['comp_rate'].mean()
    d['wavg_cmp_rate']=(res['comp_rate']*(res['vwap']*res['quantity'])/(res['vwap']*res['quantity']).sum()).sum()
    d['exe_value']=res['executed_notional'].sum()
    return d

def show(datas):
    l=[]
    d=stats(datas)
    d['group']='total'
    l.append(d)
    for gn in [['operation','opt'],['algo_name','algo'],['target_exe_time','exe-time'],['user','batch'],['account_id','account']]:
        for n,g in datas.groupby(gn[0]):
            r=stats(g)
            r['group']='{}-{}'.format(gn[1],n)
            l.append(r)
    return pd.DataFrame(l)

def show2(datas):
    l=[]
    d=stats(datas)
    d['group']='total'
    l.append(d)
    for gn in [['operation','opt'],['firm','user'],['account_id','account']]:
        for n,g in datas.groupby(gn[0]):
            r=stats(g)
            r['group']='{}-{}'.format(gn[1],n)
            l.append(r)
    return pd.DataFrame(l)

def get_klines(syms):
    results=pqdm([[_] for _ in syms],adata.stock.market.get_market_min,n_jobs=10,argument_type='args')
    df=pd.concat(results)
    df=df.rename(columns={'stock_code':'symbol','amount':'amt','volume':'qty','trade_time':'time'})
    df['amt']=df['amt'].astype(float)
    df['qty']=df['qty'].astype(float)
    df['time']=pd.to_datetime(df['time'])
    return df

# date="********"
# date=datetime.datetime.now().strftime('%Y%m%d')
# pos=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from algo_parentorder where date={}".format(date))
# pos['start_time']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)))
# pos['end_time']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)))
# pos['side']=pos['operation'].apply(lambda x:1 if x==0 else -1)
# df = ef.stock.get_quote_history(list(pos['symbol'].unique()), klt=1)
# stk_dict={}
# for k,v in df.items():
#     v=v.rename(columns={'股票代码':'symbol', '日期':'time', '开盘':'open', '收盘':'close', '最高':'high', '最低':'low', '成交量':'qty', '成交额':'amt'})
#     v=v[['symbol','time','open','close','high','low','qty','amt']]
#     v['time']=pd.to_datetime(v['time'])
#     stk_dict[k]=v
# results=[]
# for po in pos.to_dict('records'):
#     r=calc(stk_dict[po['symbol']],po,False)
#     results.append(r)
# res=pd.DataFrame(results)
# res['executed_notional']=res['filled_price']*res['filled_quantity']
# res['comp_rate']=res['filled_quantity']/res['quantity']
# res['target_exe_time']=res['end_time']-res['start_time'] 
# show2(res)

# date="********"
# pos=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from algo_parentorder where date={}".format(date))
# pos['start_time']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)))
# pos['end_time']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)))
# pos['side']=pos['operation'].apply(lambda x:1 if x==0 else -1)
# df=get_klines(list(pos['symbol'].unique()))



# date="********"
date=datetime.datetime.now().strftime('%Y%m%d')
pos=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from algo_parentorder where date={}".format(date))
pos['start_time']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)))
pos['end_time']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)))
pos['side']=pos['operation'].apply(lambda x:1 if x==0 else -1)
df=get_klines(list(pos['symbol'].unique()))
stk_dict={}
for k,v in df.groupby('symbol'):
    stk_dict[k]=v
results=[]
for po in pos.to_dict('records'):
    try:
        r=calc(stk_dict[po['symbol']],po,True)
        results.append(r)
    except KeyError:
        print('no data for symbol {}'.format(po['symbol']))
res=pd.DataFrame(results)
res['executed_notional']=res['filled_price']*res['filled_quantity']
res['comp_rate']=res['filled_quantity']/res['quantity']
res['target_exe_time']=res['end_time']-res['start_time'] 
show2(res)

print(date)

# from data.raw_data import mins_data
# import pandas as pd

# def read_datas_from_zipfile(date,symbols):
#     datas=mins_data.read_mindata_from_zip_file(date,1)
#     datas['time']=pd.to_datetime(datas['datadate'].astype(str)+" "+datas['bartime']+":00")
#     datas=datas.rename(columns={'ticker':'symbol','openprice':'open', 'closeprice':'close', 'highprice':'high', 'lowprice':'low', 'volume':'qty', 'value':'amt'})
#     datas=datas[['symbol','time','open','close','high','low','qty','amt']]
#     datas=datas[datas['symbol'].isin(symbols)]
#     d={}
#     for sym,g in datas.groupby('symbol'):
#         g=g[['time','open','close','high','low','qty','amt']]
#         d[sym]=g
#     return d

# date="20250530"
# pos=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from algo_parentorder where date={}".format(date))
# pos=pos.fillna(0)
# pos['start_time']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)))
# pos['end_time']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)))
# pos['side']=pos['operation'].apply(lambda x:1 if x==0 else -1)
# stk_dict=read_datas_from_zipfile(date,pos['symbol'].unique())
# results=[]
# for po in pos.to_dict('records'):
#     r=calc(stk_dict[po['symbol']],po,True)
#     results.append(r)
# res=pd.DataFrame(results)
# res['executed_notional']=res['filled_price']*res['filled_quantity']
# res['comp_rate']=res['filled_quantity']/res['quantity']
# res['target_exe_time']=res['end_time']-res['start_time'] 

# show2(res)

# df=ef.stock.get_realtime_quotes()









# results=[]
# for po in pos.to_dict('records'):
#     r=calc(stk_dict[po['symbol']],po,True)
#     results.append(r)
# res=pd.DataFrame(results)
# res['executed_notional']=res['filled_price']*res['filled_quantity']
# res['comp_rate']=res['filled_quantity']/res['quantity']
# res['target_exe_time']=res['end_time']-res['start_time'] 

# show2(res)