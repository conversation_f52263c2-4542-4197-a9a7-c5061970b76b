import pandas as pd
import numpy as np
import os, sys
import traceback
import datetime

# 简化logger
class SimpleLogger:
    def warning(self, msg):
        print(f"WARNING: {msg}")
    def error(self, msg):
        print(f"ERROR: {msg}")

logger = SimpleLogger()

# 注释掉原有的导入，避免依赖问题
# from misc.tools import get_stock_deal, get_stock_minimal_deal


def standardize_position_data(df, account_type='account_1'):
    """
    标准化持仓数据格式

    Args:
        df: 原始持仓数据DataFrame
        account_type: 账户类型 ('account_1' 或 'account_2')

    Returns:
        标准化后的DataFrame，包含列: ['ticker', 'volume', 'available_volume', 'close']
    """
    if account_type == 'account_1':
        # Account 1 格式: CSV文件
        standardized = pd.DataFrame()
        standardized['ticker'] = df['证券代码'].astype(str).str.replace(r'[^\d]', '', regex=True).astype(int)
        standardized['volume'] = df['总持仓'].fillna(0)
        standardized['available_volume'] = df['可卖持仓'].fillna(0)
        standardized['close'] = df['现价'].fillna(0)

    elif account_type == 'account_2':
        # Account 2 格式: Excel文件
        standardized = pd.DataFrame()
        # 处理证券代码，可能包含.SH/.SZ后缀
        ticker_col = df['证券代码'].astype(str)
        # 提取数字部分
        standardized['ticker'] = ticker_col.str.replace(r'\.S[HZ]$', '', regex=True).astype(int)
        standardized['volume'] = df['持仓数量'].fillna(0)
        standardized['available_volume'] = df.get('子单可卖数量', df['持仓数量']).fillna(0)
        standardized['close'] = df['最新价'].fillna(0)

    else:
        raise ValueError(f"Unsupported account_type: {account_type}")

    # 过滤掉持仓为0的记录
    standardized = standardized[standardized['volume'] != 0].copy()
    standardized = standardized.reset_index(drop=True)

    return standardized


def standardize_transaction_data(df, account_type='account_1', transaction_type='vwap'):
    """
    标准化交易数据格式

    Args:
        df: 原始交易数据DataFrame
        account_type: 账户类型 ('account_1' 或 'account_2')
        transaction_type: 交易类型 ('vwap' 或 't0')

    Returns:
        标准化后的DataFrame，包含列: ['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume']
    """
    if df.empty:
        return pd.DataFrame(columns=['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume'])

    standardized = pd.DataFrame()

    if account_type == 'account_1':
        # Account 1 格式
        ticker_col = df['证券代码'].astype(str).str.replace(r'[^\d]', '', regex=True)
        standardized['ticker'] = ticker_col.astype(int)
        standardized['BS_flag'] = df['方向'].map({'买': '买入', '卖': '卖出'})
        standardized['fill_price'] = pd.to_numeric(df['成交价格'], errors='coerce').fillna(0)
        standardized['fill_volume'] = pd.to_numeric(df['成交数量'], errors='coerce').fillna(0)

    elif account_type == 'account_2':
        if transaction_type == 'vwap':
            # Account 2 VWAP交易格式
            ticker_col = df['证券代码'].astype(str).str.replace(r'\.S[HZ]$', '', regex=True)
            standardized['ticker'] = ticker_col.astype(int)
            standardized['BS_flag'] = df['交易方向'].map({'买入': '买入', '卖出': '卖出'})
            standardized['fill_price'] = pd.to_numeric(df['成交均价'], errors='coerce').fillna(0)
            standardized['fill_volume'] = pd.to_numeric(df['成交数量'], errors='coerce').fillna(0)
        else:  # t0
            # Account 2 T0交易格式
            ticker_col = df['股票'].astype(str).str.replace(r'\.S[HZ]$', '', regex=True)
            standardized['ticker'] = ticker_col.astype(int)
            standardized['BS_flag'] = df['买卖'].map({'买': '买入', '卖': '卖出'})
            standardized['fill_price'] = pd.to_numeric(df['已成价格'], errors='coerce').fillna(0)
            standardized['fill_volume'] = pd.to_numeric(df['已成量'], errors='coerce').fillna(0)

    else:
        raise ValueError(f"Unsupported account_type: {account_type}")

    # 设置direction字段 (1: 多头, -1: 空头，这里简化处理，都设为1)
    standardized['direction'] = 1

    # 过滤掉无效交易
    standardized = standardized[
        (standardized['fill_volume'] > 0) &
        (standardized['fill_price'] > 0) &
        (standardized['BS_flag'].notna())
    ].copy()

    standardized = standardized.reset_index(drop=True)

    return standardized


def analyze_intraday_trading(t0_deal):
    """
    分析日内交易，计算已平仓收益和未平仓头寸

    Args:
        t0_deal: 标准化的T0交易数据，包含列: ['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume']

    Returns:
        dict: {
            'closed_pnl': 已平仓收益,
            'closed_amount': 已平仓交易金额,
            'open_positions': 未平仓头寸DataFrame,
            'open_floating_pnl': 未平仓浮动盈亏 (需要当前价格计算)
        }
    """
    if t0_deal.empty:
        return {
            'closed_pnl': 0,
            'closed_amount': 0,
            'open_positions': pd.DataFrame(columns=['ticker', 'net_volume', 'avg_price']),
            'open_floating_pnl': 0
        }

    results = {}
    closed_pnl = 0
    closed_amount = 0
    open_positions = []

    # 按股票分组处理
    for ticker, group in t0_deal.groupby('ticker'):
        # 分离买入和卖出
        buys = group[group['BS_flag'] == '买入'].copy()
        sells = group[group['BS_flag'] == '卖出'].copy()

        if buys.empty and sells.empty:
            continue

        # 按价格排序，FIFO原则
        buys = buys.sort_values('fill_price').reset_index(drop=True)
        sells = sells.sort_values('fill_price', ascending=False).reset_index(drop=True)

        # 计算买入和卖出的总量
        total_buy_volume = buys['fill_volume'].sum()
        total_sell_volume = sells['fill_volume'].sum()

        # 计算平仓量（买卖的最小值）
        closed_volume = min(total_buy_volume, total_sell_volume)

        if closed_volume > 0:
            # 计算平仓收益
            # 使用加权平均价格
            buy_avg_price = np.average(buys['fill_price'], weights=buys['fill_volume'])
            sell_avg_price = np.average(sells['fill_price'], weights=sells['fill_volume'])

            # 平仓收益 = (卖出均价 - 买入均价) * 平仓量
            ticker_closed_pnl = (sell_avg_price - buy_avg_price) * closed_volume
            closed_pnl += ticker_closed_pnl

            # 平仓交易金额
            ticker_closed_amount = closed_volume * (buy_avg_price + sell_avg_price)
            closed_amount += ticker_closed_amount

        # 计算未平仓头寸
        net_volume = total_buy_volume - total_sell_volume
        if abs(net_volume) > 0:
            if net_volume > 0:
                # 多头未平仓
                avg_price = np.average(buys['fill_price'], weights=buys['fill_volume'])
            else:
                # 空头未平仓
                avg_price = np.average(sells['fill_price'], weights=sells['fill_volume'])

            open_positions.append({
                'ticker': ticker,
                'net_volume': net_volume,
                'avg_price': avg_price
            })

    open_positions_df = pd.DataFrame(open_positions)

    return {
        'closed_pnl': closed_pnl,
        'closed_amount': closed_amount,
        'open_positions': open_positions_df,
        'open_floating_pnl': 0  # 需要当前价格才能计算
    }


def calc_account_estimate_pnl(date, pre_hold, hold, vwap_deal, t0_deal, vwap_cr, t0_cr, tax_rate=0.0005):
    """
    计算账户收益

    Args:
        date: 交易日期 '********'
        pre_hold: 前日持仓 cols: ['ticker', 'volume', 'available_volume', 'close']
        hold: 今日持仓 cols: ['ticker', 'volume', 'available_volume', 'close']
        vwap_deal: VWAP交易 cols: ['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume']
        t0_deal: T0交易 cols: ['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume']
        vwap_cr: VWAP交易费率
        t0_cr: T0交易费率
        tax_rate: 税率

    Returns:
        dict: 包含各项收益和统计指标
    """

    # 分析日内交易
    t0_analysis = analyze_intraday_trading(t0_deal)

    # 准备数据，添加direction字段
    pre_hold = pre_hold.copy()
    hold = hold.copy()

    # 设置direction字段（简化处理，根据volume正负判断）
    pre_hold['direction'] = pre_hold['volume'].apply(lambda x: 1 if x >= 0 else -1)
    pre_hold['volume'] = pre_hold['volume'].abs()

    hold['direction'] = hold['volume'].apply(lambda x: 1 if x >= 0 else -1)
    hold['volume'] = hold['volume'].abs()

    # 合并所有交易数据
    all_deals = []
    if not vwap_deal.empty:
        vwap_deal_copy = vwap_deal.copy()
        vwap_deal_copy['deal_type'] = 'vwap'
        all_deals.append(vwap_deal_copy)

    if not t0_deal.empty:
        t0_deal_copy = t0_deal.copy()
        t0_deal_copy['deal_type'] = 't0'
        all_deals.append(t0_deal_copy)

    if all_deals:
        deal = pd.concat(all_deals, ignore_index=True)
    else:
        deal = pd.DataFrame(columns=['ticker', 'direction', 'BS_flag', 'fill_price', 'fill_volume', 'deal_type'])

    # 设置交易方向和开平标志
    if not deal.empty:
        deal['direction'] = deal['BS_flag'].map(lambda x: 1 if x in ['买入', '卖出'] else -1)
        deal['OC'] = deal['BS_flag'].map(lambda x: 1 if x in ['买入', '卖空'] else -1)
        deal['fill_volume'] = deal['fill_volume'].abs()

        # 过滤无效交易
        deal = deal[deal['fill_volume'] != 0]
        deal['fill_volume'] = deal['fill_volume'] * deal['OC']

    # 检查持仓平衡：前日持仓 + 交易 = 今日持仓
    if not deal.empty:
        tmp_deal = deal[['ticker', 'direction', 'fill_volume']].groupby(['ticker', 'direction']).sum()
        computed_hold = pre_hold.set_index(['ticker', 'direction'])['volume'].add(tmp_deal['fill_volume'], fill_value=0)
        diff_deal = hold.set_index(['ticker', 'direction'])['volume'].sub(computed_hold, fill_value=0)
        diff_deal = diff_deal[diff_deal != 0]

        if not (diff_deal == 0).all():
            logger.warning(f"持仓不平衡，差异: {diff_deal.sum()}")

    # 分离开仓和平仓交易
    if not deal.empty:
        deal_open = deal[deal['OC']==1]
        deal_close = deal[deal['OC']==-1]

        # 计算开仓交易的加权平均价格
        if not deal_open.empty:
            deal_open = deal_open[['ticker', 'direction', 'fill_volume', 'fill_price', 'deal_type']].groupby(['ticker', 'direction']).agg({
                'fill_volume': 'sum',
                'fill_price': lambda x: np.average(x, weights=deal_open.loc[x.index, "fill_volume"]),
                'deal_type': 'first'
            }).reset_index().rename(columns={'fill_volume': 'open_volume', 'fill_price': 'open_price'})
        else:
            deal_open = pd.DataFrame(columns=['ticker', 'direction', 'open_volume', 'open_price', 'deal_type'])

        # 计算平仓交易的加权平均价格
        if not deal_close.empty:
            deal_close = deal_close[['ticker', 'direction', 'fill_volume', 'fill_price', 'deal_type']].groupby(['ticker', 'direction']).agg({
                'fill_volume': 'sum',
                'fill_price': lambda x: np.average(x, weights=deal_close.loc[x.index, "fill_volume"]),
                'deal_type': 'first'
            }).reset_index().rename(columns={'fill_volume': 'close_volume', 'fill_price': 'close_price'})
        else:
            deal_close = pd.DataFrame(columns=['ticker', 'direction', 'close_volume', 'close_price', 'deal_type'])
    else:
        deal_open = pd.DataFrame(columns=['ticker', 'direction', 'open_volume', 'open_price', 'deal_type'])
        deal_close = pd.DataFrame(columns=['ticker', 'direction', 'close_volume', 'close_price', 'deal_type'])

    # 合并数据
    data = pd.merge(hold, pre_hold, on=['ticker', 'direction'], how='outer', suffixes=('', '_pre'))
    data = pd.merge(data, deal_open, on=['ticker', 'direction'], how='outer')
    data = pd.merge(data, deal_close, on=['ticker', 'direction'], how='outer')

    # 获取前日收盘价（从pre_hold中的close字段）
    if 'close' in pre_hold.columns:
        pre_close = pre_hold[['ticker', 'close']].rename(columns={'close': 'close_pre'})
        data = pd.merge(data, pre_close, on=['ticker'], how='left')
    else:
        data['close_pre'] = 0

    if 'close' in hold.columns:
        curr_close = hold[['ticker', 'close']]
        data = pd.merge(data, curr_close, on=['ticker'], how='left')
    else:
        data['close'] = 0

    data = data.fillna(0)

    # 确保close列存在
    if 'close' not in data.columns:
        data['close'] = 0
    if 'close_pre' not in data.columns:
        data['close_pre'] = 0

    # 计算各项统计指标
    info = calculate_pnl_statistics(data, deal_open, deal_close, vwap_cr, t0_cr, tax_rate, t0_analysis)

    return info


def calculate_pnl_statistics(data, deal_open, deal_close, vwap_cr, t0_cr, tax_rate, t0_analysis):
    """
    计算收益统计指标

    Args:
        data: 合并后的持仓和交易数据
        deal_open: 开仓交易数据
        deal_close: 平仓交易数据
        vwap_cr: VWAP交易费率
        t0_cr: T0交易费率
        tax_rate: 税率
        t0_analysis: 日内交易分析结果

    Returns:
        dict: 包含各项收益和统计指标
    """
    info = {}

    # 分离多头和空头
    long = data[data['direction'] == 1]
    short = data[data['direction'] == -1]

    # 计算持仓市值
    info['long_value'] = long['volume'].mul(long['close']).sum()
    info['short_value'] = short['volume'].mul(short['close']).sum()
    info['pre_long_value'] = long['volume_pre'].mul(long['close_pre']).sum()
    info['pre_short_value'] = short['volume_pre'].mul(short['close_pre']).sum()

    # 计算持仓收益
    info['long_pnl'] = (
        long['volume'].mul(long['close']).sum()
        - long['volume_pre'].mul(long['close_pre']).sum()
        - long['open_volume'].mul(long['open_price']).sum()
        + long['close_volume'].mul(long['close_price']).sum()
    )
    info['short_pnl'] = - (
        short['volume'].mul(short['close']).sum()
        - short['volume_pre'].mul(short['close_pre']).sum()
        - short['open_volume'].mul(short['open_price']).sum()
        + short['close_volume'].mul(short['close_price']).sum()
    )

    # 计算交易金额
    vwap_open_amount = 0
    vwap_close_amount = 0
    t0_open_amount = 0
    t0_close_amount = 0

    if not deal_open.empty:
        vwap_open = deal_open[deal_open['deal_type'] == 'vwap']
        t0_open = deal_open[deal_open['deal_type'] == 't0']
        vwap_open_amount = vwap_open['open_volume'].abs().mul(vwap_open['open_price']).sum()
        t0_open_amount = t0_open['open_volume'].abs().mul(t0_open['open_price']).sum()

    if not deal_close.empty:
        vwap_close = deal_close[deal_close['deal_type'] == 'vwap']
        t0_close = deal_close[deal_close['deal_type'] == 't0']
        vwap_close_amount = vwap_close['close_volume'].abs().mul(vwap_close['close_price']).sum()
        t0_close_amount = t0_close['close_volume'].abs().mul(t0_close['close_price']).sum()

    info['vwap_amount'] = vwap_open_amount + vwap_close_amount
    info['t0_amount'] = t0_open_amount + t0_close_amount
    info['total_amount'] = info['vwap_amount'] + info['t0_amount']

    # 计算交易费用
    info['vwap_commission'] = info['vwap_amount'] * vwap_cr
    info['t0_commission'] = info['t0_amount'] * t0_cr
    info['total_commission'] = info['vwap_commission'] + info['t0_commission']

    # 计算税费（只对卖出收税）
    vwap_sell_amount = vwap_close_amount if not deal_close.empty else 0
    t0_sell_amount = t0_close_amount if not deal_close.empty else 0
    info['vwap_tax'] = vwap_sell_amount * tax_rate
    info['t0_tax'] = t0_sell_amount * tax_rate
    info['total_tax'] = info['vwap_tax'] + info['t0_tax']

    # 计算总收益
    info['gross_pnl'] = info['long_pnl'] + info['short_pnl']
    info['net_pnl'] = info['gross_pnl'] - info['total_commission'] - info['total_tax']

    # 日内交易相关统计
    info['t0_closed_pnl'] = t0_analysis['closed_pnl']
    info['t0_closed_amount'] = t0_analysis['closed_amount']
    info['t0_open_positions_count'] = len(t0_analysis['open_positions'])

    # 计算未平仓浮动盈亏（需要当前价格）
    open_positions = t0_analysis['open_positions']
    if not open_positions.empty:
        # 这里需要当前价格来计算浮动盈亏，暂时设为0
        info['t0_floating_pnl'] = 0
        info['t0_open_volume'] = open_positions['net_volume'].abs().sum()
    else:
        info['t0_floating_pnl'] = 0
        info['t0_open_volume'] = 0

    return info


def test_account_pnl_calculation():
    """
    测试账户收益计算函数
    """
    print("=== 测试账户收益计算函数 ===")

    # 测试Account 1
    print("\n--- 测试 Account 1 ---")
    try:
        # 读取数据
        pre_pos_1 = pd.read_csv('/home/<USER>/trade/calc_account_pnl/account_1/Position_20250707.csv')
        curr_pos_1 = pd.read_csv('/home/<USER>/trade/calc_account_pnl/account_1/Position_********.csv')
        vwap_trans_1 = pd.read_csv('/home/<USER>/trade/calc_account_pnl/account_1/transaction_********.csv')
        t0_trans_1 = pd.read_csv('/home/<USER>/trade/calc_account_pnl/account_1/t0_transaction_********.csv')

        # 标准化数据
        pre_hold_1 = standardize_position_data(pre_pos_1, 'account_1')
        hold_1 = standardize_position_data(curr_pos_1, 'account_1')
        vwap_deal_1 = standardize_transaction_data(vwap_trans_1, 'account_1', 'vwap')
        t0_deal_1 = standardize_transaction_data(t0_trans_1, 'account_1', 't0')

        print(f"前日持仓记录数: {len(pre_hold_1)}")
        print(f"当日持仓记录数: {len(hold_1)}")
        print(f"VWAP交易记录数: {len(vwap_deal_1)}")
        print(f"T0交易记录数: {len(t0_deal_1)}")

        # 计算收益
        result_1 = calc_account_estimate_pnl(
            date='********',
            pre_hold=pre_hold_1,
            hold=hold_1,
            vwap_deal=vwap_deal_1,
            t0_deal=t0_deal_1,
            vwap_cr=0.0003,  # VWAP费率
            t0_cr=0.0005,    # T0费率
            tax_rate=0.001   # 税率
        )

        print("Account 1 计算结果:")
        for key, value in result_1.items():
            if isinstance(value, (int, float)):
                print(f"  {key}: {value:.2f}")
            else:
                print(f"  {key}: {value}")

    except Exception as e:
        print(f"Account 1 测试失败: {e}")
        import traceback
        traceback.print_exc()

    # 测试Account 2
    print("\n--- 测试 Account 2 ---")
    try:
        # 读取数据
        pre_pos_2 = pd.read_excel('/home/<USER>/trade/calc_account_pnl/account_2/position_20250707.xlsx')
        curr_pos_2 = pd.read_excel('/home/<USER>/trade/calc_account_pnl/account_2/position_********.xlsx')
        vwap_trans_2 = pd.read_csv('/home/<USER>/trade/calc_account_pnl/account_2/transaction_********.csv', encoding='gbk')
        t0_trans_2 = pd.read_csv('/home/<USER>/trade/calc_account_pnl/account_2/t0_transaction_********.csv', encoding='gbk')

        # 标准化数据
        pre_hold_2 = standardize_position_data(pre_pos_2, 'account_2')
        hold_2 = standardize_position_data(curr_pos_2, 'account_2')
        vwap_deal_2 = standardize_transaction_data(vwap_trans_2, 'account_2', 'vwap')
        t0_deal_2 = standardize_transaction_data(t0_trans_2, 'account_2', 't0')

        print(f"前日持仓记录数: {len(pre_hold_2)}")
        print(f"当日持仓记录数: {len(hold_2)}")
        print(f"VWAP交易记录数: {len(vwap_deal_2)}")
        print(f"T0交易记录数: {len(t0_deal_2)}")

        # 计算收益
        result_2 = calc_account_estimate_pnl(
            date='********',
            pre_hold=pre_hold_2,
            hold=hold_2,
            vwap_deal=vwap_deal_2,
            t0_deal=t0_deal_2,
            vwap_cr=0.0003,  # VWAP费率
            t0_cr=0.0005,    # T0费率
            tax_rate=0.001   # 税率
        )

        print("Account 2 计算结果:")
        for key, value in result_2.items():
            if isinstance(value, (int, float)):
                print(f"  {key}: {value:.2f}")
            else:
                print(f"  {key}: {value}")

    except Exception as e:
        print(f"Account 2 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_account_pnl_calculation()
    
    
    
    
    
    
    
    
    
    
    
def compute_comprehensive_account():
    hold['direction'] = hold['volume'].map(lambda x: 1 if x>0 else -1)
    hold['volume'] = hold['volume'].abs()
    pre_hold['direction'] = pre_hold['volume'].map(lambda x: 1 if x>0 else -1)
    pre_hold['volume'] = pre_hold['volume'].abs()

    deal['direction'] = deal['BS_flag'].map(lambda x: 1 if x in ['买入', '卖出'] else -1) 
    deal['OC'] = deal['BS_flag'].map(lambda x: 1 if x in ['买入', '卖空'] else -1)
    deal['fill_volume'] = deal['fill_volume'].abs()

    deal = deal[deal['fill_volume'] != 0]

    deal['fill_volume'] = deal['fill_volume'] * deal['OC']
    print(deal.head(1))

    # check morning_hold + deal == hold
    tmp_deal = deal[['ticker', 'direction', 'fill_volume']].groupby(['ticker', 'direction']).sum()
    computed_hold = pre_hold.set_index(['ticker', 'direction'])['volume'].add(tmp_deal['fill_volume'], fill_value=0)
    diff_deal = hold.set_index(['ticker', 'direction'])['volume'].sub(computed_hold, fill_value=0)
    diff_deal = diff_deal[diff_deal != 0]
    if not (diff_deal == 0).all():
        diff_deal = diff_deal.to_frame('fill_volume').reset_index()
        diff_deal = pd.merge(diff_deal, close, on=['ticker'], how='left')
        diff_deal['OC'] = np.where(diff_deal['fill_volume'] > 0, 1, -1)
    
        logger.error(f'{self.account_name} morning_hold + deal != hold,  diff: \n{diff_deal}')
        confirm_flag = input("合并diff_deal 到 deal 中? y/n")
        if confirm_flag.lower() == 'y':
            diff_deal['fill_price'] = diff_deal['close']
            deal = pd.concat([deal, diff_deal], axis=0, ignore_index=True)
        
        
    deal_open = deal[deal['OC']==1]
    deal_close = deal[deal['OC']==-1]

    deal_open = deal_open[['ticker', 'direction', 'fill_volume', 'fill_price']].groupby(['ticker', 'direction']).agg(
        {'fill_volume': 'sum', 'fill_price': lambda x: np.average(x, weights=deal_open.loc[x.index, "fill_volume"])}
    ).reset_index().rename(columns={'fill_volume': 'open_volume', 'fill_price': 'open_price'})

    deal_close = deal_close[['ticker', 'direction', 'fill_volume', 'fill_price']].groupby(['ticker', 'direction']).agg(
        {'fill_volume': 'sum', 'fill_price': lambda x: np.average(x, weights=deal_close.loc[x.index, "fill_volume"])}
    ).reset_index().rename(columns={'fill_volume': 'close_volume', 'fill_price': 'close_price'})

    # deal = deal[['ticker', 'direction', 'fill_volume', 'fill_price']].groupby(['ticker', 'direction']).agg(
    #     {'fill_volume': 'sum', 'fill_price': lambda x: np.average(x, weights=deal.loc[x.index, "fill_volume"])}
    # ).reset_index()

    data = pd.merge(hold, morning_hold, on=['ticker', 'direction'], how='outer', suffixes=('', '_pre'))
    data = pd.merge(data, close, on=['ticker'], how='left' )
    data = pd.merge(data, pre_close, on=['ticker'], how='left', suffixes=('', '_pre'))
    data = pd.merge(data, deal_open, on=['ticker', 'direction'], how='outer')
    data = pd.merge(data, deal_close, on=['ticker', 'direction'], how='outer')

    data = data.fillna(0)
    # data columns
    # ticker  direction  volume  volume_pre  fill_volume  fill_price  close  close_pre

    info = {}

    long = data[data['direction'] == 1]
    short = data[data['direction'] == -1]
    info['long_value'] = long['volume'].mul(long['close']).sum()
    info['short_value'] = short['volume'].mul(short['close']).sum()
    info['pre_long_value'] = long['volume_pre'].mul(long['close_pre']).sum()
    info['pre_short_value'] = short['volume_pre'].mul(short['close_pre']).sum()

    info['long_pnl'] = (
        long['volume'].mul(long['close']).sum()
        - long['volume_pre'].mul(long['close_pre']).sum()
        - long['open_volume'].mul(long['open_price']).sum()
        - long['close_volume'].mul(long['close_price']).sum()
    )
    info['short_pnl'] = - (
        short['volume'].mul(short['close']).sum()
        - short['volume_pre'].mul(short['close_pre']).sum()
        - short['open_volume'].mul(short['open_price']).sum()
        - short['close_volume'].mul(short['close_price']).sum()
    )

    info["stock_commission"] = (
        data["open_volume"].abs().mul(data["open_price"]).sum()
        + data["close_volume"].abs().mul(data["close_price"]).sum()
    ) * (self.stock_commission_rate)

    info["stock_tax"] = data["close_volume"].abs().mul(
        data["close_price"]
    ).sum() * (self.stock_tax_rate)

    info["account_pnl"] = (
        info["long_pnl"]
        + info["short_pnl"]
        - info["stock_commission"]
        - info["stock_tax"]
    )

    # # detail record
    # data['pnl'] = (data['volume'].mul(data['close'])
    #     - data['volume_pre'].mul(data['close_pre'])
    #     - data['open_volume'].mul(data['open_price'])
    #     - data['close_volume'].mul(data['close_price'])
    # )
    # data['pnl'] = np.where(data['direction']==1, data['pnl'], -data['pnl'])
    # data['stock_commission'] = ((data['open_volume']*data['open_price']).abs() + ((data['close_volume']*data['close_price']).abs())).mul(self.stock_commission_rate)
    # data['stock_tax'] = (data['close_volume']*data['close_price']).abs().mul(self.stock_tax_rate)
    # data['account_pnl'] = data['pnl'] - data['stock_commission'] - data['stock_tax']
    # write_file(data, file_type='xls', dest_type='dav', dest_path=os.path.join(self.account_name, 'account', 'detail_pnl_{}.xls'.format(self.trade_date)), index=False)


    accountinfo_path = os.path.join(self.account_name, 'account', f'accountinfo_{self.trade_date}.xls')
    accountinfo = read_remote_file(accountinfo_path, src_type='dav')
    # merge
    info = pd.concat([accountinfo, pd.DataFrame(info, index=[0]) ], axis=1)

    if self.account_type == 'stockaccount':
        accout_file_path = os.path.join(self.account_name, 'account', f'stockaccount_{self.trade_date}.xls')
        write_file(info, file_type='xls', dest_type='dav', dest_path=accout_file_path, index=False)
    elif self.account_type == 'marginaccount':
        accout_file_path = os.path.join(self.account_name, 'account', f'marginaccount_{self.trade_date}.xls')
        write_file(info, file_type='xls', dest_type='dav', dest_path=accout_file_path, index=False)

    return info