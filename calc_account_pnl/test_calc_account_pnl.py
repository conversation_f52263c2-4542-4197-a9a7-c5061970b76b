#!/usr/bin/env python3
"""
测试账户收益计算函数的单元测试
"""

import pandas as pd
import numpy as np
import unittest
import sys
import os

# 添加路径以便导入模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from calc_account_pnl import (
    standardize_position_data,
    standardize_transaction_data,
    analyze_intraday_trading,
    calc_account_estimate_pnl
)


class TestAccountPnlCalculation(unittest.TestCase):
    """账户收益计算测试类"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建测试用的持仓数据
        self.test_position_account1 = pd.DataFrame({
            '证券代码': ['000001', '000002', '300001'],
            '总持仓': [1000, 2000, 1500],
            '可卖持仓': [1000, 2000, 1500],
            '现价': [10.5, 15.2, 25.8]
        })
        
        self.test_position_account2 = pd.DataFrame({
            '证券代码': ['600001.SH', '000001.SZ', '300001.SZ'],
            '持仓数量': [1000, 2000, 1500],
            '子单可卖数量': [1000, 2000, 1500],
            '最新价': [10.5, 15.2, 25.8]
        })
        
        # 创建测试用的交易数据
        self.test_transaction_account1 = pd.DataFrame({
            '证券代码': ['000001', '000002', '000001'],
            '方向': ['买', '卖', '卖'],
            '成交价格': [10.0, 15.5, 10.2],
            '成交数量': [500, 1000, 300]
        })
        
        self.test_transaction_account2_vwap = pd.DataFrame({
            '证券代码': ['600001.SH', '000001.SZ'],
            '交易方向': ['买入', '卖出'],
            '成交均价': [10.0, 15.5],
            '成交数量': [500, 1000]
        })
        
        self.test_transaction_account2_t0 = pd.DataFrame({
            '股票': ['600001.SH', '000001.SZ', '600001.SH'],
            '买卖': ['买', '卖', '卖'],
            '已成价格': [10.0, 15.5, 10.1],
            '已成量': [200, 500, 100]
        })
    
    def test_standardize_position_data_account1(self):
        """测试Account 1持仓数据标准化"""
        result = standardize_position_data(self.test_position_account1, 'account_1')
        
        self.assertEqual(len(result), 3)
        self.assertListEqual(list(result.columns), ['ticker', 'volume', 'available_volume', 'close'])
        self.assertEqual(result.iloc[0]['ticker'], 1)
        self.assertEqual(result.iloc[0]['volume'], 1000)
        self.assertEqual(result.iloc[0]['close'], 10.5)
    
    def test_standardize_position_data_account2(self):
        """测试Account 2持仓数据标准化"""
        result = standardize_position_data(self.test_position_account2, 'account_2')
        
        self.assertEqual(len(result), 3)
        self.assertListEqual(list(result.columns), ['ticker', 'volume', 'available_volume', 'close'])
        self.assertEqual(result.iloc[0]['ticker'], 600001)
        self.assertEqual(result.iloc[0]['volume'], 1000)
        self.assertEqual(result.iloc[0]['close'], 10.5)
    
    def test_standardize_transaction_data_account1(self):
        """测试Account 1交易数据标准化"""
        result = standardize_transaction_data(self.test_transaction_account1, 'account_1', 'vwap')

        self.assertEqual(len(result), 3)
        expected_cols = ['ticker', 'BS_flag', 'fill_price', 'fill_volume', 'direction']
        self.assertListEqual(list(result.columns), expected_cols)
        self.assertEqual(result.iloc[0]['ticker'], 1)
        self.assertEqual(result.iloc[0]['BS_flag'], '买入')
        self.assertEqual(result.iloc[0]['fill_price'], 10.0)
    
    def test_standardize_transaction_data_account2_vwap(self):
        """测试Account 2 VWAP交易数据标准化"""
        result = standardize_transaction_data(self.test_transaction_account2_vwap, 'account_2', 'vwap')

        self.assertEqual(len(result), 2)
        expected_cols = ['ticker', 'BS_flag', 'fill_price', 'fill_volume', 'direction']
        self.assertListEqual(list(result.columns), expected_cols)
        self.assertEqual(result.iloc[0]['ticker'], 600001)
        self.assertEqual(result.iloc[0]['BS_flag'], '买入')
    
    def test_standardize_transaction_data_account2_t0(self):
        """测试Account 2 T0交易数据标准化"""
        result = standardize_transaction_data(self.test_transaction_account2_t0, 'account_2', 't0')

        self.assertEqual(len(result), 3)
        expected_cols = ['ticker', 'BS_flag', 'fill_price', 'fill_volume', 'direction']
        self.assertListEqual(list(result.columns), expected_cols)
        self.assertEqual(result.iloc[0]['ticker'], 600001)
        self.assertEqual(result.iloc[0]['BS_flag'], '买入')
    
    def test_analyze_intraday_trading(self):
        """测试日内交易分析"""
        # 创建测试T0交易数据
        t0_data = pd.DataFrame({
            'ticker': [1, 1, 1, 2, 2],
            'direction': [1, 1, 1, 1, 1],
            'BS_flag': ['买入', '卖出', '卖出', '买入', '买入'],
            'fill_price': [10.0, 10.5, 10.3, 15.0, 15.2],
            'fill_volume': [1000, 500, 300, 800, 200]
        })
        
        result = analyze_intraday_trading(t0_data)
        
        # 检查返回的字典结构
        expected_keys = ['closed_pnl', 'closed_amount', 'open_positions', 'open_floating_pnl']
        for key in expected_keys:
            self.assertIn(key, result)
        
        # 检查已平仓收益计算
        self.assertGreater(result['closed_pnl'], 0)  # 应该有正收益
        self.assertGreater(result['closed_amount'], 0)  # 应该有交易金额
        
        # 检查未平仓头寸
        self.assertIsInstance(result['open_positions'], pd.DataFrame)
        self.assertGreaterEqual(len(result['open_positions']), 0)
    
    def test_calc_account_estimate_pnl_basic(self):
        """测试基本的账户收益计算"""
        # 准备测试数据
        pre_hold = pd.DataFrame({
            'ticker': [1, 2],
            'volume': [1000, 2000],
            'available_volume': [1000, 2000],
            'close': [10.0, 15.0]
        })
        
        hold = pd.DataFrame({
            'ticker': [1, 2],
            'volume': [1200, 1800],
            'available_volume': [1200, 1800],
            'close': [10.5, 15.2]
        })
        
        vwap_deal = pd.DataFrame({
            'ticker': [1],
            'direction': [1],
            'BS_flag': ['买入'],
            'fill_price': [10.2],
            'fill_volume': [200]
        })
        
        t0_deal = pd.DataFrame({
            'ticker': [2],
            'direction': [1],
            'BS_flag': ['卖出'],
            'fill_price': [15.1],
            'fill_volume': [200]
        })
        
        result = calc_account_estimate_pnl(
            date='********',
            pre_hold=pre_hold,
            hold=hold,
            vwap_deal=vwap_deal,
            t0_deal=t0_deal,
            vwap_cr=0.0003,
            t0_cr=0.0005,
            tax_rate=0.001
        )
        
        # 检查返回结果的基本结构
        expected_keys = [
            'long_value', 'short_value', 'pre_long_value', 'pre_short_value',
            'long_pnl', 'short_pnl', 'vwap_amount', 't0_amount', 'total_amount',
            'vwap_commission', 't0_commission', 'total_commission',
            'vwap_tax', 't0_tax', 'total_tax',
            'gross_pnl', 'net_pnl', 't0_closed_pnl', 't0_closed_amount'
        ]
        
        for key in expected_keys:
            self.assertIn(key, result)
            self.assertIsInstance(result[key], (int, float, np.integer, np.floating))
    
    def test_empty_data_handling(self):
        """测试空数据处理"""
        # 创建空的但有正确列名的DataFrame
        empty_pos_df = pd.DataFrame(columns=['证券代码', '总持仓', '可卖持仓', '现价'])
        empty_trans_df = pd.DataFrame(columns=['证券代码', '方向', '成交价格', '成交数量'])

        # 测试空持仓数据
        result_pos = standardize_position_data(empty_pos_df, 'account_1')
        self.assertEqual(len(result_pos), 0)

        # 测试空交易数据
        result_trans = standardize_transaction_data(empty_trans_df, 'account_1', 'vwap')
        self.assertEqual(len(result_trans), 0)

        # 测试空T0交易分析
        empty_t0_df = pd.DataFrame()
        result_t0 = analyze_intraday_trading(empty_t0_df)
        self.assertEqual(result_t0['closed_pnl'], 0)
        self.assertEqual(result_t0['closed_amount'], 0)
        self.assertEqual(len(result_t0['open_positions']), 0)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
